## 本说明是Air780E  GSM模块的AT指令说明文档，请根据文档编制代码
## 下面的AT指令测试是通过串口助手连接模块实测数据
## AT指令收到的信息都打印输出，输出信息使用英文，中文会乱码的
## AT指令发送后 while等待接收回复，如果超时 打印输出AT指令+ERROR 表示这条指令设置失败了
## GSM模块与单品硬件连接使用LPUART1串口 波特率 115200
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Src里新建GSM.c文件
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Inc里新建GSM.h文件
## 代码中文注释 打印输出使用英文
##################################################
关闭回显：
[15:47:08.054]发→◇ATE0
□
[15:47:08.058]收←◆
OK

获取模组型号：
[15:47:50.325]发→◇AT+CGMM
□
[15:47:50.333]收←◆
+CGMM: "Air780EG"

OK

获取CCID号：
[15:48:16.934]发→◇AT+CCID
□
[15:48:16.942]收←◆
89860316249511500582

OK

获取模块电压：
[15:49:06.613]发→◇AT+CBC
□
[15:49:06.619]收←◆
+CBC: 3469

OK

查询是否驻网：
[15:55:09.934]发→◇AT+CREG?
□
[15:55:09.940]收←◆
+CREG: 0,1      （这里有可能是1 有可能是2 不要计较）

OK

获取4G信号值：
[15:50:06.061]发→◇AT+CSQ
□
[15:50:06.069]收←◆
+CSQ: 24,0

OK

连接TCP服务器：（服务器IP和端口要放到头文件下面使用宏定义 方便修改）
[16:02:54.877]发→◇AT+CIPSTART="TCP","************",48085
□
[16:02:54.888]收←◆
OK

[16:02:54.931]收←◆
CONNECT OK     （重点判断是否收到这个回复 收到表示连接服务器成功 如果没收到就是没有连接 超时2秒）


注意：连接TCP服务器的AT指令只能发送一次，如果没有及时检测到 CONNECT OK 这个回复，执行了重发 会收到下面的信息：
[10:07:58.349]发→◇AT+CIPSTART="TCP","************",48085
□
[10:07:58.360]收←◆
+CME ERROR: 65535


ALREADY CONNECT

如果代码没有及时检测到 CONNECT OK  这个连接成功的回复，盲目执行重发，而又没有检测到错误，就会误认为设备没有连接服务器，所以设计确认策略时要注意这个问题；




关闭服务器连接：（执行设备休眠流程时要关闭服务器连接）
[16:04:16.349]发→◇AT+CIPCLOSE
□
[16:04:16.366]收←◆
CLOSE OK

设置非透传快发模式：
[16:05:29.045]发→◇AT+CIPQSEND=1
□
[16:05:29.052]收←◆
OK

发送数据：
[16:08:57.134]发→◇AT+CIPSEND=122  （先发AT指令+数据长度后 再发实际要发的数据 这个指令要内嵌一个自动计算数据长度的代码 重发时也要先执行这个指令 再发送数据）
□
[16:08:57.141]收←◆
>
[16:08:58.123]发→◇HY122S+11957.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E
□
[16:08:58.142]收←◆
DATA ACCEPT:122 （AT指令响应回复）

[16:08:58.250]收←◆ACCEPT  （服务器响应回复 收到ACCEPT表示服务器收到了信息）







